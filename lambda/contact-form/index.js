const uuid = require("uuid");
const { putRow } = require("../dynamo");
const {
  DYNAMO_TABLES,
  FORM_TYPES,
  URWAIRPORT_EMAIL_TEMPLATES,
} = require("../constants");
const { addMember } = require("../mailchimp");
const ContentfulApi = require("../contentful/contentfulApi");
const { sendTemplate } = require("../mailchimp/templates");
const {
  sendConnectFormEmail,
  sendRegisterNowFormEmail,
  sendBusinessOpportunitiesFormEmail,
  sendEmploymentOpportunitiesFormEmail
} = require("../csv-exporter/ses");
exports.handler = async (event) => {
  let response;
  let statusCode;
  let featuredNews;
  let vars;

  // Generate a unique request ID for tracking
  const requestId = uuid.v4();
  console.log(`[${requestId}] Processing contact form request`);

  const headers = {
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS", // Allow only GET request
  };

  if (process.env.NODE_ENV === "local") {
    headers["Access-Control-Allow-Origin"] = "*";
  }

  if (event.httpMethod === "OPTIONS") {
    console.log(`[${requestId}] OPTIONS request handled`);
    return {
      statusCode: 200,
      headers,
    };
  }

  if (!event.body) {
    console.error(`[${requestId}] No request body provided`);
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "No data provided",
      }),
    };
  }

  let data;
  try {
    data = JSON.parse(event.body);
    console.log(`[${requestId}] Request parsed successfully`, {
      formType: data.formType,
      hasFormData: !!data.formData
    });
  } catch (parseError) {
    console.error(`[${requestId}] Failed to parse request body:`, {
      error: parseError.message,
      body: event.body
    });
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({
        message: "Invalid JSON in request body",
      }),
    };
  }

  const id = uuid.v4();
  console.log(`[${requestId}] Form type: ${data.formType}`);
  console.log(`[${requestId}] Form data:`, JSON.stringify(data.formData, null, 2));

  try {
    switch (data.formType) {
      case FORM_TYPES.CONTACT_US:
        console.log(`[${requestId}] Processing CONTACT_US form`);
        await putRow(DYNAMO_TABLES.CONTACT_RESPONSE, data.formData);
        console.log(`[${requestId}] Successfully saved to DynamoDB`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);
        await sendConnectFormEmail(data.formData);
        console.log(`[${requestId}] Successfully sent connect form email`);
        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.JFK_CONTACT_US:
        console.log(`[${requestId}] Processing JFK_CONTACT_US form`);
        await putRow(DYNAMO_TABLES.CONTACT_RESPONSE, data.formData);
        console.log(`[${requestId}] Successfully saved to DynamoDB`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);
        await sendConnectFormEmail(data.formData);
        console.log(`[${requestId}] Successfully sent connect form email`);
        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.STAY_UP_TO_DATE:
        console.log(`[${requestId}] Processing STAY_UP_TO_DATE form`);
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        console.log(`[${requestId}] Successfully saved to DynamoDB with responseId: ${id}`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);

        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;

      case FORM_TYPES.REGISTER_NOW:
        console.log(`[${requestId}] Processing REGISTER_NOW form`);
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        console.log(`[${requestId}] Successfully saved to DynamoDB with responseId: ${id}`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);
        await sendRegisterNowFormEmail(data.formData);
        console.log(`[${requestId}] Successfully sent register now form email`);

        console.log(`[${requestId}] Fetching featured news from Contentful`);
        featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        console.log(`[${requestId}] Successfully fetched featured news`);
        vars = [
          {
            name: "featuredNews",
            content: featuredNews,
          },
        ];

        console.log(`[${requestId}] Sending template email to: ${data.formData.email}`);
        await sendTemplate(
          "URW Airports - You're on our list",
          URWAIRPORT_EMAIL_TEMPLATES.REGISTER_NOW,
          data.formData.email,
          vars
        );
        console.log(`[${requestId}] Successfully sent template email`);

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.BUSINESS_OPPORTUNITY:
        console.log(`[${requestId}] Processing BUSINESS_OPPORTUNITY form`);
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        console.log(`[${requestId}] Successfully saved to DynamoDB with responseId: ${id}`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);
        await sendBusinessOpportunitiesFormEmail(data.formData);
        console.log(`[${requestId}] Successfully sent business opportunities form email`);
        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;
      case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
        console.log(`[${requestId}] Processing EMPLOYMENT_OPPORTUNITY form`);
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        console.log(`[${requestId}] Successfully saved to DynamoDB with responseId: ${id}`);
        await addMember(data.formData, data.formType);
        console.log(`[${requestId}] Successfully added to Mailchimp`);
        await sendEmploymentOpportunitiesFormEmail(data.formData);
        console.log(`[${requestId}] Successfully sent employment opportunities form email`);
        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;

      default:
        console.error(`[${requestId}] Invalid form type provided: ${data.formType}`);
        statusCode = 400;
        response = {
          message: "Invalid form type provided",
        };
    }
    console.log(`[${requestId}] Request completed successfully with status: ${statusCode}`);
    return {
      statusCode,
      headers,
      body: JSON.stringify(response),
    };
  } catch (error) {
    // Enhanced error logging with full context
    const errorDetails = {
      requestId,
      formType: data?.formType || 'unknown',
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      timestamp: new Date().toISOString(),
      formDataKeys: data?.formData ? Object.keys(data.formData) : [],
      httpMethod: event.httpMethod,
      userAgent: event.headers?.['User-Agent'] || 'unknown',
      sourceIp: event.requestContext?.identity?.sourceIp || 'unknown'
    };

    console.error(`[${requestId}] Error processing contact form:`, JSON.stringify(errorDetails, null, 2));

    // Log the original error object for additional context
    console.error(`[${requestId}] Original error object:`, error);

    // Return a user-friendly error message without exposing internal details
    return {
      headers,
      statusCode: 500,
      body: JSON.stringify({
        message: "An error occurred while processing your request. Please try again later.",
        requestId: requestId // Include request ID for support purposes
      }),
    };
  }
};
