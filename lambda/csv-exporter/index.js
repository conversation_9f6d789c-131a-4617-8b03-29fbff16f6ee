const { getRows, getWeeklyRows } = require("../dynamo");
const { exportToCsvAndS3, downloadFromS3, getExistingRecords } = require("./fileExport");
const { sendEmail } = require("./ses");
const { DYNAMO_TABLES, FORM_TYPES, FILES, DAILY_FILES, WEEKLY_FILES } = require("../constants");

exports.handler = async (event = {}) => {
  try {
    // Parse event input to determine schedule type and forms to process
    const schedule = event.schedule || 'daily'; // default to daily for backward compatibility
    const formsToProcess = event.forms || ['contactUs', 'stayUpToDate', 'registerNow']; // default to all forms

    console.log(`Processing ${schedule} export for forms: ${formsToProcess.join(', ')}`);

    // Determine which data fetching function to use
    const dataFetcher = schedule === 'weekly' ? getWeeklyRows : getRows;

    // Get data from dynamoDB based on forms to process
    console.log("Fetching data from DynamoDB ...");
    const promises = [];
    let contactUs = { Items: [] };
    let registerNow = { Items: [] };
    let stayUpToDate = { Items: [] };

    if (formsToProcess.includes('contactUs')) {
      promises.push(dataFetcher(DYNAMO_TABLES.CONTACT_RESPONSE));
    } else {
      promises.push(Promise.resolve({ Items: [] }));
    }

    if (formsToProcess.includes('registerNow')) {
      promises.push(dataFetcher(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.REGISTER_NOW));
    } else {
      promises.push(Promise.resolve({ Items: [] }));
    }

    if (formsToProcess.includes('stayUpToDate')) {
      promises.push(dataFetcher(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.STAY_UP_TO_DATE));
    } else {
      promises.push(Promise.resolve({ Items: [] }));
    }

    [contactUs, registerNow, stayUpToDate] = await Promise.all(promises);
    // Filter files based on forms to process
    const filesToProcess = FILES.filter(file => {
      if (file.form === FORM_TYPES.CONTACT_US && formsToProcess.includes('contactUs')) return true;
      if (file.form === FORM_TYPES.STAY_UP_TO_DATE && formsToProcess.includes('stayUpToDate')) return true;
      if (file.form === FORM_TYPES.REGISTER_NOW && formsToProcess.includes('registerNow')) return true;
      return false;
    });

    console.log(`Processing ${filesToProcess.length} files for forms: ${formsToProcess.join(', ')}`);

    // Get existing files from S3
    console.log("Downloading existing files from S3 ...");
    const downloads = filesToProcess.map((file) => downloadFromS3(file));

    await Promise.all(downloads);

    const existingCSVRecords = filesToProcess.map((file) => getExistingRecords(file));
    const existingRecordsResults = await Promise.all(existingCSVRecords);

    // Create a map for easier access to existing records
    const existingRecordsMap = {};
    filesToProcess.forEach((file, index) => {
      existingRecordsMap[file.form] = existingRecordsResults[index];
    });

    console.log("Processing records for export...");

    // Process records only for forms that are being processed
    const recordsMap = {};

    if (formsToProcess.includes('contactUs')) {
      recordsMap[FORM_TYPES.CONTACT_US] = getContactUsRecordsToWrite(
        contactUs.Items,
        existingRecordsMap[FORM_TYPES.CONTACT_US] || []
      );
      console.log(`Contact Us records to write: ${recordsMap[FORM_TYPES.CONTACT_US].length}`);
    }

    if (formsToProcess.includes('stayUpToDate')) {
      recordsMap[FORM_TYPES.STAY_UP_TO_DATE] = getStayUpToDateRecordsToWrite(
        stayUpToDate.Items,
        existingRecordsMap[FORM_TYPES.STAY_UP_TO_DATE] || []
      );
      console.log(`Stay Up To Date records to write: ${recordsMap[FORM_TYPES.STAY_UP_TO_DATE].length}`);
    }

    if (formsToProcess.includes('registerNow')) {
      recordsMap[FORM_TYPES.REGISTER_NOW] = getRegisterNowRecordsToWrite(
        registerNow.Items,
        existingRecordsMap[FORM_TYPES.REGISTER_NOW] || []
      );
      console.log(`Register Now records to write: ${recordsMap[FORM_TYPES.REGISTER_NOW].length}`);
    }

    // const registerNowHoltToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowHolt);
    // const registerNowLeasingToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowLeasing);
    // const registerNowOpsToWrite = getRegisterNowRecordsToWrite(registerNow.Items, existingRegisterNowOps);

    // const ntoBusinessToWrite = getNtoBusinessOpportunitiesRecordsToWrite(ntoBusiness.Items, existingNtoBusiness);
    // console.log(`NTO Business records to write: ${ntoBusinessToWrite.length}`);

    // const ntoEmploymentToWrite = getNtoEmploymentOpportunitiesRecordsToWrite(ntoEmployment.Items, existingNtoEmployment);
    // console.log(`NTO Employment records to write: ${ntoEmploymentToWrite.length}`);


    console.log("Saving new data to downloaded files ... ");
    // Save new data to downloaded files and upload back to S3
    const exports = filesToProcess.map((file) => {
      console.log(`Processing file: ${file.fileName} with form type: ${file.form}`);

      const records = recordsMap[file.form];
      if (!records) {
        console.log(`No records found for form type: ${file.form}, skipping...`);
        return Promise.resolve(null);
      }

      return exportToCsvAndS3(records, file);
    });

    console.log(`Waiting for ${exports.length} export operations to complete...`);
    const results = await Promise.all(exports);
    const validResults = results.filter(r => r !== null);
    console.log(`Successfully completed ${validResults.length} export operations`);

    console.log("Sending emails for exported files...");
    const emails = validResults.map((r) => sendEmail(r));

    const emailResult = await Promise.all(emails);
    console.log(`Successfully sent ${emailResult.length} emails`);

    return {
      statusCode: 200,
      body: JSON.stringify({ emailResult, results: validResults }),
    };
  } catch (error) {
    console.error("Error in CSV exporter handler:", error);
    console.error("Error stack:", error.stack);

    // Log additional context for debugging
    console.error("Error details:", {
      message: error.message,
      name: error.name,
      stack: error.stack
    });

    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message,
        details: "Check CloudWatch logs for more information"
      }),
    };
  }
};

const getContactUsRecordsToWrite = (records, existingRecords) => {
  let existingContactUsRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        company: item.Company,
        createdAt: item.Date,
        email: item.Email,
        firstName: item.Firstname,
        lastName: item.Lastname,
        message: item.Message,
        mobile: item.Mobile,
        propertyId: item.Airport,
        emailUpdates: item["Email Updates"],
      };
    });

  const recordsToWrite = [...records, ...existingContactUsRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getStayUpToDateRecordsToWrite = (records, existingRecords) => {
  let existingStayUpToDateRecords = existingRecords
    .filter((item) => item.Mobile != "")
    .map((item) => {
      return {
        createdAt: item.Date,
        email: item.Email,
        propertyId: item.Airport,
        responseId: item.ResponseId,
      };
    });

  const recordsToWrite = [...records, ...existingStayUpToDateRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getRegisterNowRecordsToWrite = (records, existingRecords) => {
  let existingRegisterNowRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        createdAt: item.Date,
        responseId: item.ResponseId,
        propertyId: item.Airport,
        firstName: item.Firstname,
        lastName: item.Lastname,
        email: item.Email,
        mobile: item.Mobile,
        company: item["Company Name"],
        website: item.Website,
        aboutBusiness: item["About Business"],
        "bi/architectureDesignEngineering": item["Architecture/Design Engineering"],
        "bi/construction": item["Construction"],
        "bi/foodAndBeverage": item["Food & Beverage"],
        "bi/other": item["Other"],
        "bi/productManufacturer": item["Product Manufacturer/Maker"],
        "bi/retailBusinessBrickMortar": item["Retail Business (Brick & Mortar)"],
        "bi/retailBusinessOnline": item["Retail Business (Online)"],
        "bi/professionalServices": item["Professional Services"],
        "bi/supplier": item["Supplier"],
        "mi/chicago": item["Chicago (ORD)"],
        "mi/losAngles": item["Los Angeles (LAX)"],
        "mi/newYork": item["New York (JFK T8 & New Terminal One)"],
        "cert/acdbe": item["ACDBE - Airport Concessions Disadvantaged Business Enterprise"],
        "cert/lbe": item["LBE - Local Business Enterprise"],
        "cert/mbe": item["MBE - Minority Business Enterprise"],
        "cert/sdvosb": item["SDVOSB - Service-Disabled Veteran-Owned Small Businesses"],
        "cert/wbe": item["WBE - Women Business Enterprise"],
        "cert/sbe": item["SBE - Small Business Enterprise"],
        "cert/notCertified": item["Not Certified"],
        "cert/other": item["Other"],
        otherInformation: item["Anything you would like us to know"],
        registerNowEmailUpdates: item["Email Updates"],
      };
    });

  const recordsToWrite = [...records, ...existingRegisterNowRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getNtoBusinessOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingBusinessOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        aboutBusiness: item["About Business"],
        advancedNetworkNews: item["Advanced Network News"],
        airportExperience: item["Airport Experience"],
        "bi/architectureDesignEngineering": item["Design"],
        "bi/construction": item["Construction"],
        "bi/concessions": item["Concessions"],
        "bi/other": item["Other"],
        "bi/services": item["Services"],
        businessAddress1: item["Business Address 1"],
        businessAddress2: item["Business Address 2"],
        "cert/acdbe": item["ACDBE - Airport Concessions Disadvantaged Business Enterprise"],
        "cert/lbe": item["LBE - Local Business Enterprise"],
        "cert/mbe": item["MBE - Minority Business Enterprise"],
        "cert/sdvosb": item["SDVOSB - Service-Disabled Veteran-Owned Small Businesses"],
        "cert/wbe": item["WBE - Women Business Enterprise"],
        "cert/sbe": item["SBE - Small Business Enterprise"],
        "cert/notCertified": item["Not Certified"],
        "cert/other": item["Other"],
        city: item.City,
        companyName: item["Company Name"],
        createdAt: item.Date,
        email: item.Email,
        emailUpdates: item["Email Updates"],
        firstName: item.Firstname,
        lastName: item.Lastname,
        mobile: item.Mobile,
        propertyId: item.Airport,
        responseId: item.ResponseId,
        state: item.State,
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        website: item.Website,
        yearsInBusiness: item["Years In Business"],
        zipCode: item.ZipCode,
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingBusinessOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getNtoEmploymentOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingEmploymentOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        advancedNetworkNews: item["Advanced Network News"],
        airportEmploymentExperience: item["Airport Employment Experience"],
        city: item["City"],
        createdAt: item["Date"],
        "ei/construction": item["Construction"],
        "ei/foodAndBeverage": item["Food & Beverage"],
        "ei/maintenance": item["Maintenance"],
        "ei/professionalServices": item["Professional Services"],
        "ei/retail": item["Retail"],
        email: item["Email"],
        emailUpdates: item["Email Updates"],
        firstName: item["Firstname"],
        homeAddress1: item["Home Address 1"],
        homeAddress2: item["Home Address 2"],
        lastName: item["Lastname"],
        mobile: item["Mobile"],
        propertyId: item["Airport"],
        responseId: item["ResponseId"],
        state: item["State"],
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        zipCode: item["ZipCode"],
        locale: item["Locale"],
      };
    });

  const recordsToWrite = [...records, ...existingEmploymentOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};
